"use client";
import React, { useEffect, useState } from "react";
import { CheckCircle, Loader2, AlertCircle } from "lucide-react";
import Link from "next/link";
import { useSearchParams } from "next/navigation";
import { usePaymentConfirmation } from "@/hooks/use-query";

interface PaymentDetails {
  session_id?: string;
  payment_intent?: string;
  purchase_type?: string;
  item_id?: string;
  amount?: string;
}

const Success = () => {
  const searchParams = useSearchParams();
  const [paymentDetails, setPaymentDetails] = useState<PaymentDetails>({});
  const [isConfirming, setIsConfirming] = useState(true);
  const [confirmationError, setConfirmationError] = useState<string | null>(null);

  const { mutate: confirmPayment } = usePaymentConfirmation();

  useEffect(() => {
    // Extract payment details from URL parameters
    const details: PaymentDetails = {
      session_id: searchParams.get("session_id") || undefined,
      payment_intent: searchParams.get("payment_intent") || undefined,
      purchase_type: searchParams.get("purchase_type") || undefined,
      item_id: searchParams.get("item_id") || undefined,
      amount: searchParams.get("amount") || undefined,
    };

    setPaymentDetails(details);

    // Confirm payment with backend if we have session_id
    if (details.session_id) {
      confirmPayment(
        { session_id: details.session_id },
        {
          onSuccess: () => {
            setIsConfirming(false);
          },
          onError: (error: any) => {
            setIsConfirming(false);
            setConfirmationError(
              error?.response?.data?.message || "Failed to confirm payment"
            );
          },
        }
      );
    } else {
      // No session_id, assume payment was already processed
      setIsConfirming(false);
    }
  }, [searchParams, confirmPayment]);

  if (isConfirming) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
        <div className="max-w-md w-full bg-white rounded-xl shadow-lg p-8 text-center">
          <div className="mb-6 flex justify-center">
            <Loader2 className="w-16 h-16 text-blue-500 animate-spin" strokeWidth={2} />
          </div>
          <h1 className="text-2xl font-bold text-gray-900 mb-3">
            Confirming Payment...
          </h1>
          <p className="text-gray-600">
            Please wait while we confirm your payment details.
          </p>
        </div>
      </div>
    );
  }

  if (confirmationError) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
        <div className="max-w-md w-full bg-white rounded-xl shadow-lg p-8 text-center">
          <div className="mb-6 flex justify-center">
            <AlertCircle className="w-16 h-16 text-orange-500" strokeWidth={2} />
          </div>
          <h1 className="text-2xl font-bold text-gray-900 mb-3">
            Payment Confirmation Issue
          </h1>
          <p className="text-gray-600 mb-6">
            Your payment was processed, but we encountered an issue confirming it.
            Please contact support with your payment details.
          </p>
          <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
            <p className="text-sm text-red-700">{confirmationError}</p>
          </div>
          <div className="border-t border-gray-100 pt-6 space-y-3">
            <Link href="/contact">
              <button className="w-full bg-orange-500 hover:bg-orange-600 text-white font-semibold py-2 px-6 rounded-lg transition-colors">
                Contact Support
              </button>
            </Link>
            <Link href="/">
              <button className="w-full bg-gray-500 hover:bg-gray-600 text-white font-semibold py-2 px-6 rounded-lg transition-colors">
                Back to Home
              </button>
            </Link>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
      <div className="max-w-md w-full bg-white rounded-xl shadow-lg p-8 text-center">
        <div className="mb-6 flex justify-center">
          <CheckCircle className="w-16 h-16 text-green-500" strokeWidth={2} />
        </div>

        <h1 className="text-2xl font-bold text-gray-900 mb-3">
          Payment Successful!
        </h1>

        <p className="text-gray-600 mb-6">
          Thank you for your purchase. We&apos;ve sent a confirmation email with
          your order details.
        </p>

        {/* Display payment details if available */}
        {(paymentDetails.purchase_type || paymentDetails.amount) && (
          <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
            <h3 className="font-semibold text-green-800 mb-2">Purchase Details</h3>
            {paymentDetails.purchase_type && (
              <p className="text-sm text-green-700">
                Service: {paymentDetails.purchase_type.replace('_', ' ').toUpperCase()}
              </p>
            )}
            {paymentDetails.amount && (
              <p className="text-sm text-green-700">
                Amount: €{paymentDetails.amount}
              </p>
            )}
            {paymentDetails.session_id && (
              <p className="text-xs text-green-600 mt-2">
                Reference: {paymentDetails.session_id.slice(-8)}
              </p>
            )}
          </div>
        )}

        <div className="border-t border-gray-100 pt-6">
          <Link href="/">
            <button className="bg-green-500 hover:bg-green-600 text-white font-semibold py-2 px-6 rounded-lg transition-colors">
              Back to Home
            </button>
          </Link>
        </div>
      </div>
    </div>
  );
};

export default Success;
