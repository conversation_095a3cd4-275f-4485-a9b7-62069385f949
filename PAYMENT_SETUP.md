# Payment Integration Setup Guide

This document explains how to set up the payment integration for the Career Ireland Immigration SaaS platform.

## Overview

The payment system handles purchases for:
- Immigration packages
- Mentor services  
- Training programs
- Regular packages

## Architecture

```
User Purchase → Frontend → Backend Payment API → Payment Provider → Webhook → Backend Confirmation → Database
```

## Setup Instructions

### 1. Environment Variables

Copy `.env.example` to `.env.local` and configure the following:

```bash
# Payment Provider (Stripe)
STRIPE_PUBLISHABLE_KEY=pk_test_...
STRIPE_SECRET_KEY=sk_test_...
WEBHOOK_SECRET=whsec_...

# Backend API
NEXT_PUBLIC_API_URL=http://localhost:8000/api
BACKEND_API_KEY=your-server-to-server-key
```

### 2. Stripe Webhook Configuration

1. Go to your Stripe Dashboard → Webhooks
2. Add endpoint: `https://yourdomain.com/api/webhook`
3. Select events:
   - `checkout.session.completed`
   - `payment_intent.succeeded`
   - `payment_intent.payment_failed`
4. Copy the webhook secret to `WEBHOOK_SECRET`

### 3. Backend API Endpoints Required

Your backend must implement these endpoints:

#### Payment Creation Endpoints (Already exist)
- `POST /payment/guest-immigration`
- `POST /payment/guest-package`
- `POST /payment/guest-service`
- `POST /payment/guest-training`
- `POST /payment/immigration-service` (authenticated)
- `POST /payment/package` (authenticated)
- `POST /payment/mentor-service` (authenticated)
- `POST /payment/training` (authenticated)

#### New Endpoints Needed
- `POST /payment/confirm` - Handle webhook confirmations
- `POST /payment/confirm-session` - Confirm payment by session ID
- `GET /payment/status/:payment_id` - Check payment status

### 4. Payment Flow

#### For Guest Users:
1. User fills guest form with name, email, mobile
2. Frontend calls `/payment/guest-{type}` with user data + item ID
3. Backend creates payment session with metadata
4. User redirected to payment provider
5. After payment, user redirected to `/payment/success?session_id=...`
6. Frontend calls `/payment/confirm-session` to verify
7. Webhook confirms payment and saves to database

#### For Authenticated Users:
1. User clicks purchase button
2. Frontend calls `/payment/{type}` with item ID + auth token
3. Backend creates payment session with user data from token
4. Rest of flow same as guest users

### 5. Metadata Structure

Payment sessions should include this metadata:

```json
{
  "purchase_type": "immigration_service|package|mentor_service|training",
  "item_id": "uuid-of-purchased-item",
  "user_email": "<EMAIL>",
  "user_name": "John Doe",
  "user_mobile": "+************"
}
```

### 6. Database Schema

Ensure your backend can store:
- Purchase records with payment status
- Link purchases to users (for authenticated) or guest data
- Payment provider transaction IDs
- Purchase timestamps and amounts

### 7. Testing

#### Test the webhook locally:
```bash
# Install Stripe CLI
stripe listen --forward-to localhost:3000/api/webhook

# Test with sample events
stripe trigger checkout.session.completed
```

#### Test payment flow:
1. Use Stripe test card: `************** 4242`
2. Any future expiry date
3. Any 3-digit CVC

### 8. Security Considerations

- Always verify webhook signatures
- Use HTTPS in production
- Store sensitive keys as environment variables
- Implement rate limiting on webhook endpoint
- Log all payment events for audit trail

### 9. Error Handling

The system handles these scenarios:
- Payment success but webhook fails → Manual reconciliation needed
- Payment fails → User redirected to failure page
- Network issues → Retry mechanisms in place
- Invalid webhook signatures → Rejected with 400 status

### 10. Monitoring

Monitor these metrics:
- Payment success/failure rates
- Webhook delivery success
- Time between payment and confirmation
- Failed confirmation attempts

## Troubleshooting

### Common Issues:

1. **Webhook not receiving events**
   - Check webhook URL is accessible
   - Verify webhook secret matches
   - Check Stripe dashboard for delivery attempts

2. **Payment successful but no data saved**
   - Check webhook logs
   - Verify backend `/payment/confirm` endpoint
   - Check database connectivity

3. **Signature verification fails**
   - Ensure webhook secret is correct
   - Check request body is raw (not parsed)
   - Verify timestamp tolerance

### Debug Steps:

1. Check browser network tab for API calls
2. Check backend logs for webhook receipts
3. Check Stripe dashboard for webhook delivery status
4. Verify environment variables are loaded correctly

## Support

For issues with this payment integration, check:
1. Application logs
2. Stripe dashboard
3. Backend API logs
4. Database records

Contact the development team with specific error messages and transaction IDs.
