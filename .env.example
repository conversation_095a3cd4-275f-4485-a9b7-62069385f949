# Next.js Configuration
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=your-nextauth-secret-here

# Google OAuth (if using Google authentication)
GOOGLE_ID=your-google-client-id
GOOGLE_SECRET=your-google-client-secret

# Backend API Configuration
NEXT_PUBLIC_API_URL=http://localhost:8000/api
NEXT_PUBLIC_IMAGE_URL=http://localhost:8000

# Payment Provider Configuration (Stripe)
STRIPE_PUBLISHABLE_KEY=pk_test_your-stripe-publishable-key
STRIPE_SECRET_KEY=sk_test_your-stripe-secret-key

# Webhook Configuration
WEBHOOK_SECRET=whsec_your-webhook-secret-from-stripe
BACKEND_API_KEY=your-backend-server-to-server-api-key

# Database Configuration (if needed for local development)
DATABASE_URL=postgresql://username:password@localhost:5432/careerireland

# Email Configuration (if needed)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password

# Other Configuration
NODE_ENV=development
