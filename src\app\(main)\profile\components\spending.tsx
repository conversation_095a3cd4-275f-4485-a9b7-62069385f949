import { Card, CardContent } from "@/components/ui/card";
import { EuroIcon } from "lucide-react";
import React from "react";
interface SpendingProps {
  totalSpent: string;
  servicesCount: number;
  packagesCount: number;
  immigrationCount: number;
}

export function Spending({
  totalSpent,
  servicesCount,
  packagesCount,
  immigrationCount,
}: SpendingProps) {
  return (
    <Card className="mb-8 border-[#404bd0]/20 bg-white">
      <CardContent className="pt-6">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <div className="p-4 rounded-lg bg-[#404bd0]/5 border border-[#404bd0]/10">
            <div className="flex items-center gap-2 text-sm text-muted-foreground mb-2">
              <EuroIcon className="w-4 h-4" />
              <span>Total Spent</span>
            </div>
            <div className="text-2xl font-bold text-[#404bd0]">
              €{totalSpent.toLocaleString()}
            </div>
          </div>

          <div className="p-4 rounded-lg bg-[#404bd0]/5 border border-[#404bd0]/10">
            <div className="text-sm text-muted-foreground mb-2">
              Mentor Services
            </div>
            <div className="text-2xl font-bold">{servicesCount}</div>
          </div>

          <div className="p-4 rounded-lg bg-[#404bd0]/5 border border-[#404bd0]/10">
            <div className="text-sm text-muted-foreground mb-2">Packages</div>
            <div className="text-2xl font-bold">{packagesCount}</div>
          </div>

          <div className="p-4 rounded-lg bg-[#404bd0]/5 border border-[#404bd0]/10">
            <div className="text-sm text-muted-foreground mb-2">
              Immigration Services
            </div>
            <div className="text-2xl font-bold">{immigrationCount}</div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
