import { NextRequest, NextResponse } from "next/server";
import { headers } from "next/headers";
import <PERSON><PERSON> from "stripe";

// This should match your payment provider (Stripe, PayPal, etc.)
const WEBHOOK_SECRET = process.env.WEBHOOK_SECRET;
const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: "2024-11-20.acacia",
});

export async function POST(req: NextRequest) {
  try {
    const body = await req.text();
    const headersList = headers();
    const signature =
      headersList.get("stripe-signature") ||
      headersList.get("webhook-signature");

    if (!WEBHOOK_SECRET) {
      console.error("WEBHOOK_SECRET is not configured");
      return NextResponse.json(
        { error: "Webhook secret not configured" },
        { status: 500 }
      );
    }

    if (!signature) {
      console.error("No signature found in webhook headers");
      return NextResponse.json(
        { error: "No signature found" },
        { status: 400 }
      );
    }

    // Parse the webhook payload
    let event: Stripe.Event;
    try {
      // For Stripe webhooks - adjust this based on your payment provider
      event = stripe.webhooks.constructEvent(body, signature, WEBHOOK_SECRET);
    } catch (err: any) {
      console.error("Webhook signature verification failed:", err.message);
      return NextResponse.json({ error: "Invalid signature" }, { status: 400 });
    }

    // Handle the event
    switch (event.type) {
      case "checkout.session.completed":
        await handlePaymentSuccess(event.data.object);
        break;
      case "payment_intent.succeeded":
        await handlePaymentSuccess(event.data.object);
        break;
      case "payment_intent.payment_failed":
        await handlePaymentFailure(event.data.object);
        break;
      default:
        console.log(`Unhandled event type: ${event.type}`);
    }

    return NextResponse.json({ received: true });
  } catch (error: any) {
    console.error("Webhook error:", error);
    return NextResponse.json(
      { error: "Webhook handler failed" },
      { status: 500 }
    );
  }
}

async function handlePaymentSuccess(paymentData: any) {
  try {
    const { metadata, customer_email, amount_total } = paymentData;

    if (!metadata) {
      console.error("No metadata found in payment data");
      return;
    }

    const { purchase_type, item_id, user_email, user_name, user_mobile } =
      metadata;

    // Prepare the confirmation data
    const confirmationData = {
      purchase_type,
      item_id,
      user_email: user_email || customer_email,
      user_name,
      user_mobile,
      amount: amount_total / 100, // Convert from cents
      payment_status: "completed",
      payment_id: paymentData.id,
      payment_intent_id: paymentData.payment_intent,
    };

    // Send confirmation to backend
    const backendUrl = process.env.NEXT_PUBLIC_API_URL;
    const response = await fetch(`${backendUrl}/payment/confirm`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${process.env.BACKEND_API_KEY}`, // Server-to-server auth
      },
      body: JSON.stringify(confirmationData),
    });

    if (!response.ok) {
      const errorData = await response.text();
      console.error("Backend confirmation failed:", errorData);
      throw new Error(`Backend confirmation failed: ${response.status}`);
    }

    const result = await response.json();
    console.log("Payment confirmation successful:", result);
  } catch (error: any) {
    console.error("Error handling payment success:", error);
    // You might want to implement retry logic here
    // or store failed confirmations for manual processing
  }
}

async function handlePaymentFailure(paymentData: any) {
  try {
    console.log("Payment failed:", paymentData.id);

    // You can implement failure handling logic here
    // such as notifying the user or updating records
  } catch (error: any) {
    console.error("Error handling payment failure:", error);
  }
}

// Handle GET requests (for webhook verification)
export async function GET() {
  return NextResponse.json({ message: "Webhook endpoint is active" });
}
