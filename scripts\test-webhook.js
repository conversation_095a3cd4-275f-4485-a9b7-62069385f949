#!/usr/bin/env node

/**
 * Simple script to test the webhook endpoint
 * Usage: node scripts/test-webhook.js
 */

const http = require('http');

const testWebhookEndpoint = async () => {
  const options = {
    hostname: 'localhost',
    port: 3000,
    path: '/api/webhook',
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
    },
  };

  return new Promise((resolve, reject) => {
    const req = http.request(options, (res) => {
      let data = '';

      res.on('data', (chunk) => {
        data += chunk;
      });

      res.on('end', () => {
        console.log('Status Code:', res.statusCode);
        console.log('Response:', data);
        resolve({ statusCode: res.statusCode, data });
      });
    });

    req.on('error', (error) => {
      console.error('Error:', error);
      reject(error);
    });

    req.end();
  });
};

const main = async () => {
  console.log('Testing webhook endpoint...');
  
  try {
    await testWebhookEndpoint();
    console.log('✅ Webhook endpoint is accessible');
  } catch (error) {
    console.error('❌ Webhook endpoint test failed:', error.message);
    process.exit(1);
  }
};

if (require.main === module) {
  main();
}

module.exports = { testWebhookEndpoint };
