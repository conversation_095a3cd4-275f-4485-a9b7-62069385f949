"use client";
import React from "react";
import { format } from "date-fns";
import { Mail } from "lucide-react";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import Setting from "./setting";
import DeleteAccount from "./delete-account";

const Profile = ({ user }: { user: IProfile }) => {
  const [isOpen, setIsOpen] = React.useState(false);
  return (
    <>
      <Card className="mb-8 border-[#404bd0]/20 bg-white">
        <CardContent className="pt-6">
          <div className="flex flex-col md:flex-row items-center gap-6">
            <Avatar className="w-24 h-24 border-2 border-[#404bd0]/20">
              <AvatarImage src={user.image || undefined} />
              <AvatarFallback className="text-2xl bg-[#404bd0]/10 text-[#404bd0] uppercase">
                {user.name.slice(0, 2)}
              </AvatarFallback>
            </Avatar>
            <div className="flex flex-col md:flex-row items-center md:items-start gap-4 flex-1">
              <div className="space-y-2 flex flex-col justify-center items-center md:justify-start md:items-start">
                <h1 className="capitalize text-xl font-semibold">
                  {user.name}
                </h1>
                <div className="flex items-center gap-2 text-muted-foreground">
                  <Mail className="w-4 h-4" />
                  <span>{user.email}</span>
                </div>
                <div className="text-sm text-muted-foreground">
                  Member since {format(new Date(user.createdAt), "MMMM yyyy")}
                </div>
              </div>
            </div>
            <DropdownMenu modal={false}>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="outline"
                  className="border-[#404bd0]/20 hover:bg-[#404bd0]/5 hover:border-[#404bd0]/30 text-[#404bd0]"
                >
                  Account Setting
                </Button>
              </DropdownMenuTrigger>
              <Setting user={user} setIsOpen={setIsOpen} />
            </DropdownMenu>
          </div>
        </CardContent>
      </Card>
      <DeleteAccount setIsOpen={setIsOpen} isOpen={isOpen} />
    </>
  );
};

export default Profile;
